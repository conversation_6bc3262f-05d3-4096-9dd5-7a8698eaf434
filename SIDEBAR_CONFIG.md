# 侧边栏配置说明

## 当前配置
- **默认显示**: 侧边栏在页面加载1秒后自动显示
- **快捷键**: Ctrl+Shift+B (Mac: Command+Shift+B) 切换显示/隐藏
- **关闭方式**: ESC键、点击遮罩、点击关闭按钮

## 回退到Popup模式
如需回退到原始popup模式，执行以下步骤：

### 方法1: 快速回退
```bash
# 进入项目目录
cd "/Users/<USER>/Downloads/project 7"

# 恢复备份文件
cp backup/* ./

# 重新加载扩展
```

### 方法2: 配置切换
可以添加配置选项在popup和sidebar模式间切换：

1. 在options.html中添加显示模式选择
2. 在content.js中根据配置决定是否显示侧边栏
3. 保留popup.html作为备用界面

## 当前功能状态
- ✅ 侧边栏自动显示
- ✅ 快捷键切换
- ✅ 所有原有功能保留
- ✅ 样式隔离
- ✅ 响应式设计

## 已移除的快捷键
为避免冲突，已移除以下快捷键：
- Ctrl+Shift+S (快速收集)
- Ctrl+Shift+A (AI总结)
- Ctrl+Shift+C (收集选中文本)
- Ctrl+Shift+O (打开配置)

这些功能现在通过侧边栏界面操作。

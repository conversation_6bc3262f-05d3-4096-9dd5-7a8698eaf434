# 善思 Blinko 智能收集器

一个现代化的Chrome扩展，用于智能收集网页内容到Blinko笔记系统。采用全新的界面设计，支持AI总结、个人想法记录、智能标签管理等功能。

## ✨ 功能特点

### 🎨 全新界面设计
- **现代化UI**：参考flomo设计风格，简洁美观
- **智能布局**：自动识别页面信息，分区域展示
- **响应式设计**：适配不同屏幕尺寸
- **流畅交互**：优化的用户体验和动画效果

### 🔗 智能链接识别
- **自动提取**：自动识别页面标题和URL
- **实时显示**：在界面顶部清晰展示页面信息
- **格式优化**：智能格式化链接显示

### 📄 AI智能摘要
- **一键生成**：点击按钮即可生成AI摘要
- **可编辑内容**：支持手动编辑AI生成的摘要
- **多模型支持**：支持OpenAI、Claude、DeepSeek等
- **智能优化**：根据页面类型优化提示词

### 💭 个人想法记录
- **独立区域**：专门的个人想法输入区域
- **自由编辑**：支持多行文本输入
- **思维记录**：与AI摘要分离，记录个人思考

### 🏷️ 智能标签系统
- **自动生成**：基于页面内容智能生成标签
- **可视化编辑**：直观的标签添加和删除
- **分类优化**：智能分类规则，提高标签准确性
- **手动管理**：支持手动添加和删除标签

### 🚀 一键提交
- **内容整合**：自动整合摘要、想法和标签
- **格式化输出**：生成结构化的笔记内容
- **快速保存**：一键提交到Blinko系统

## 🛠️ 技术特性

### 智能分类规则
扩展内置了多种内容分类规则：
- **技术开发类**：GitHub、Stack Overflow、技术博客
- **学习教育类**：在线课程、教育平台、知识网站
- **新闻资讯类**：新闻网站、科技媒体、行业资讯
- **工具效率类**：生产力工具、插件推荐、效率应用
- **设计创意类**：设计平台、创意网站、艺术作品

### 支持的AI模型
- **OpenAI**：GPT-3.5、GPT-4、GPT-4o系列
- **Claude**：Anthropic的Claude模型
- **DeepSeek**：国产优秀AI模型
- **通义千问**：阿里云AI模型
- **硅基流动**：多种开源模型支持
- **自定义**：兼容OpenAI API格式的服务

### 快捷键支持
- `Ctrl+Shift+S` (Mac: `Cmd+Shift+S`) - 快速收集当前页面
- `Ctrl+Shift+A` (Mac: `Cmd+Shift+A`) - AI总结当前文章
- `Ctrl+Shift+C` (Mac: `Cmd+Shift+C`) - 收集选中文本
- `Ctrl+Shift+O` (Mac: `Cmd+Shift+O`) - 打开配置页面

## 📦 安装使用

### 1. 安装扩展
1. 下载扩展文件到本地
2. 打开Chrome扩展管理页面 (`chrome://extensions/`)
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择扩展文件夹

### 2. 配置设置
1. 点击扩展图标，点击右上角设置按钮
2. 配置Blinko API地址和Token
3. 配置AI服务（可选，用于AI摘要功能）
4. 保存设置

### 3. 开始使用
1. 在任意网页点击扩展图标
2. 查看自动识别的页面信息
3. 点击"AI总结"生成摘要（可编辑）
4. 在"个人想法"区域添加你的思考
5. 管理智能生成的标签
6. 点击"提交到Blinko"保存

## ⚙️ 配置说明

### Blinko API配置
- **API地址**：你的Blinko服务API地址
  - 格式：`https://your-domain.com/api/v1/note/upsert`
  - 系统会自动检测和修正API地址格式
- **Token**：在Blinko设置中获取的API Token
  - 支持多种Token格式，系统会自动处理

### AI服务配置（可选）
- **服务商**：选择AI服务提供商
- **API密钥**：对应服务的API密钥
- **API地址**：自定义API服务地址
- **模型选择**：根据需要选择合适的AI模型
- **高级参数**：温度、最大Token数、Top P等参数
- **自定义提示词**：配置AI总结的风格和要求

### 智能分类配置
- **启用智能分类**：自动分类页面内容
- **自动添加标签**：根据分类自动生成标签
- **域名标签**：添加基于域名的标签
- **时间标签**：添加时间相关标签
- **置信度阈值**：分类置信度的最低要求

## 🎯 使用场景

### 📚 学习研究
- 收集学术论文和技术文档
- AI总结帮助快速理解核心内容
- 智能分类便于知识管理

### 💼 工作效率
- 收集行业资讯和竞品分析
- 快速整理会议资料和项目文档
- 团队知识库建设

### 🌐 信息整理
- 收集感兴趣的文章和博客
- 整理购物清单和旅行攻略
- 个人知识库构建

## 🔧 技术特性

### 架构设计
- **模块化设计**：清晰的代码结构，易于维护和扩展
- **异步处理**：非阻塞操作，不影响浏览体验
- **错误处理**：完善的异常处理和用户反馈

### 安全性
- **本地存储**：敏感信息本地加密存储
- **权限最小化**：只请求必要的浏览器权限
- **数据保护**：不收集用户隐私数据

### 兼容性
- **Chrome 88+**：支持主流Chrome版本
- **跨平台**：Windows、macOS、Linux全平台支持
- **响应式设计**：适配不同屏幕尺寸

## � 开发说明

### 文件结构
```
├── manifest.json      # 扩展配置文件
├── popup.html         # 新版弹窗界面
├── popup.js          # 弹窗交互逻辑
├── background.js     # 后台服务和AI处理
├── content.js        # 页面内容脚本
├── options.html      # 配置页面
├── options.js        # 配置页面逻辑
├── test.html         # 功能测试页面
└── icons/            # 图标文件
```

### 核心功能模块
- **页面信息提取**：自动提取标题、URL、内容等
- **AI摘要生成**：调用AI服务生成内容摘要
- **智能分类**：基于规则的内容分类系统
- **标签管理**：智能标签生成和手动管理
- **内容整合**：将各部分内容整合为结构化笔记

### 主要API使用
- `chrome.tabs` - 获取当前页面信息
- `chrome.scripting` - 注入脚本提取页面内容
- `chrome.storage` - 存储用户配置信息
- `chrome.contextMenus` - 右键菜单功能
- `chrome.commands` - 快捷键支持
- `chrome.runtime` - 消息传递和后台处理

## 📝 更新日志

### v2.1.0 (最新)
**🎯 选中文本AI总结功能**
- 新增选中文本AI总结：支持对页面选中文本进行专门的AI总结
- 可编辑总结内容：支持编辑AI生成的总结结果
- 内容移动功能：可将总结移动到原文摘要或个人想法区域
- 智能显示/隐藏：根据是否有选中文本自动显示相应区域

**🎨 界面优化**
- 增大AI总结显示区域，提供更好的阅读体验
- 新增右侧显示选项，支持弹窗位置自定义
- 优化选中内容区域的视觉设计和布局

**🛡️ 回退机制**
- 添加选中文本功能开关，可完全禁用该功能
- 提供功能重置选项，确保稳定性
- 完善的错误处理和用户反馈

### v2.0.1
**🐛 问题修复**
- 修复AI总结功能无法显示内容的问题
- 修复消息端口过早关闭导致的通信失败
- 增强错误处理和调试信息
- 改进AI配置检查和用户反馈
- 添加降级处理选项（AI失败时支持手动输入）
- 优化异步操作的消息传递机制

### v2.0.0
**🎨 全新界面设计**
- 🎨 **全新界面设计**：采用现代化UI，参考flomo风格
- 🔗 **智能链接识别**：自动提取和显示页面信息
- 📄 **可编辑AI摘要**：支持编辑AI生成的摘要内容
- 💭 **个人想法记录**：独立的个人思考记录区域
- 🏷️ **可视化标签管理**：直观的标签添加、编辑和删除
- 🚀 **一键提交功能**：整合所有内容一键保存
- 📱 **响应式设计**：优化不同屏幕尺寸的显示效果
- 🔧 **代码重构**：优化代码结构和性能

### v1.2.0
- 新增智能分析收集功能
- 优化AI总结质量
- 增强错误处理和用户反馈
- 支持更多AI服务商

### v1.1.0
- 新增划词收集功能
- 添加快捷键支持
- 优化界面设计
- 增强智能分类

### v1.0.0
- 基础收集功能
- AI总结支持
- Blinko集成

## 🧪 测试功能

项目包含了一个测试页面 `test.html`，用于验证扩展的各项功能：
- 丰富的内容结构测试AI摘要生成
- 多种元素类型测试内容提取
- 完整的使用场景模拟

在浏览器中打开测试页面，然后使用扩展功能进行测试。

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

### 贡献指南
1. Fork 项目：[https://github.com/tangchunwu/blinko-smart-collector](https://github.com/tangchunwu/blinko-smart-collector)
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 开发环境
1. 克隆项目：`git clone https://github.com/tangchunwu/blinko-smart-collector.git`
2. 安装依赖：项目为纯前端，无需额外依赖
3. 加载扩展：在Chrome中加载开发版本

### 提交规范
- 使用清晰的commit信息
- 遵循现有代码风格
- 添加必要的注释和文档

## 💬 支持

如果你觉得这个项目有用，请给它一个⭐️！

### 联系方式
- **项目主页**：[https://github.com/tangchunwu/blinko-smart-collector](https://github.com/tangchunwu/blinko-smart-collector)
- **问题反馈**：[Issues](https://github.com/tangchunwu/blinko-smart-collector/issues)
- **功能建议**：[Discussions](https://github.com/tangchunwu/blinko-smart-collector/discussions)
- **开发者邮箱**：[<EMAIL>](mailto:<EMAIL>)

### 致谢
- [Blinko](https://github.com/blinko-space/blinko) - 优秀的笔记系统
- 各大AI服务商提供的强大API支持
- 开源社区的贡献和反馈

---

**善思 Blinko 智能收集器** - 让知识收集更智能，让思考记录更简单。